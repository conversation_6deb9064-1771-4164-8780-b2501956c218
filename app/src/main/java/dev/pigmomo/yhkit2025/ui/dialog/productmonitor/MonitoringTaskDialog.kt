package dev.pigmomo.yhkit2025.ui.dialog.productmonitor

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringTaskResult
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.productmonitor.MonitoringStatusUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控任务列表弹窗
 * @param onDismiss 关闭弹窗回调
 */
@Composable
fun MonitoringTaskDialog(
    onDismiss: () -> Unit,
    onShowAddDialog: () -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // 状态管理
    var monitoringPlans by remember { mutableStateOf<List<MonitoringPlanEntity>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var isExecuting by remember { mutableStateOf(false) }
    var executionResults by remember { mutableStateOf<Map<Int, MonitoringTaskResult>>(emptyMap()) }
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    var selectedPlan by remember { mutableStateOf<MonitoringPlanEntity?>(null) }
    var showEditDialog by remember { mutableStateOf(false) }

    // 获取服务实例
    val monitoringPlanRepository =
        remember { MonitoringServiceManager.getMonitoringPlanRepository(context) }
    val schedulerService = remember { MonitoringServiceManager.getSchedulerService(context) }

    // 监控调度器状态
    var isSchedulerRunning by remember { mutableStateOf(false) }

    // 检查调度器状态
    LaunchedEffect(Unit) {
        isSchedulerRunning = schedulerService.isSchedulerRunning()
    }

    // 标签页选项
    val tabs = listOf("全部", "间隔", "定时", "手动")

    // 加载监控计划
    LaunchedEffect(Unit) {
        scope.launch {
            try {
                monitoringPlanRepository.getAllMonitoringPlans().collect { plans ->
                    monitoringPlans = plans
                    isLoading = false
                }
            } catch (e: Exception) {
                isLoading = false
            }
        }
    }

    // 根据选中的标签页过滤数据
    val filteredPlans = remember(monitoringPlans, selectedTabIndex) {
        when (selectedTabIndex) {
            0 -> monitoringPlans // 全部
            1 -> monitoringPlans.filter { it.operationType == MonitoringOperationType.INTERVAL }
            2 -> monitoringPlans.filter { it.operationType == MonitoringOperationType.SCHEDULED }
            3 -> monitoringPlans.filter { it.operationType == MonitoringOperationType.MANUAL }
            else -> monitoringPlans
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {},
        title = {
            Column {
                Text("监控任务列表")

                // 调度器状态显示
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = if (isSchedulerRunning) Color.Blue else Color.Red,
                                shape = CircleShape
                            )
                    )

                    Spacer(modifier = Modifier.width(6.dp))

                    Text(
                        text = if (isSchedulerRunning) "调度器运行中" else "调度器已停止",
                        fontSize = 12.sp,
                        color = if (isSchedulerRunning) Color.Green else Color.Red
                    )
                }
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 600.dp)
            ) {

                // 操作按钮栏
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(top = 2.dp, bottom = 2.dp)
                        .fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier.horizontalScroll(rememberScrollState())
                    ) {
                        TokenActionButton(
                            icon = R.drawable.outline_monitoring_24,
                            text = "执行全部",
                            onClick = {
                                if (!isExecuting) {
                                    scope.launch {
                                        isExecuting = true
                                        try {
                                            val results =
                                                schedulerService.forceExecuteAllEnabledPlans()
                                            executionResults =
                                                results.associate { it.first.id to it.second }
                                        } catch (e: Exception) {
                                            // 处理错误
                                        } finally {
                                            isExecuting = false
                                        }
                                    }
                                }
                            }
                        )

                        TokenActionButton(
                            icon = R.drawable.outline_timer_24,
                            text = "启动调度",
                            onClick = {
                                scope.launch {
                                    try {
                                        if (!schedulerService.isSchedulerRunning()) {
                                            schedulerService.startScheduler()
                                        }
                                        isSchedulerRunning = schedulerService.isSchedulerRunning()
                                    } catch (e: Exception) {
                                        // 处理启动错误
                                    }
                                }
                            }
                        )

                        TokenActionButton(
                            icon = R.drawable.outline_timer_off_24,
                            text = "停止调度",
                            onClick = {
                                scope.launch {
                                    try {
                                        if (schedulerService.isSchedulerRunning()) {
                                            schedulerService.stopScheduler()
                                        }
                                        isSchedulerRunning = schedulerService.isSchedulerRunning()
                                    } catch (e: Exception) {
                                        // 处理停止错误
                                    }
                                }
                            }
                        )

                        TokenActionButton(
                            imageVector = Icons.Default.Refresh,
                            text = "刷新",
                            onClick = {
                                scope.launch {
                                    isLoading = true
                                    try {
                                        monitoringPlanRepository.getAllMonitoringPlans()
                                            .collect { plans ->
                                                monitoringPlans = plans
                                                isLoading = false
                                            }
                                    } catch (e: Exception) {
                                        isLoading = false
                                    }
                                }
                            }
                        )

                        TokenActionButton(
                            imageVector = Icons.Default.Add,
                            text = "添加任务",
                            onClick = {
                                onShowAddDialog()
                            }
                        )
                    }
                }

                // 标签页
                TabRow(
                    selectedTabIndex = selectedTabIndex,
                    modifier = Modifier.padding(vertical = 8.dp)
                ) {
                    tabs.forEachIndexed { index, title ->
                        Tab(
                            selected = selectedTabIndex == index,
                            onClick = { selectedTabIndex = index },
                            text = { Text(title) }
                        )
                    }
                }

                // 任务列表
                if (isLoading) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                } else if (filteredPlans.isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "暂无监控任务",
                            color = Color.Gray
                        )
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(filteredPlans) { plan ->
                            MonitoringTaskItem(
                                plan = plan,
                                executionResult = executionResults[plan.id],
                                isExecuting = isExecuting,
                                onExecute = { selectedPlan ->
                                    scope.launch {
                                        try {
                                            val result =
                                                schedulerService.executeManualTask(selectedPlan.id)
                                            executionResults =
                                                executionResults + (selectedPlan.id to result)
                                        } catch (e: Exception) {
                                            // 处理错误
                                        }
                                    }
                                },
                                onShowDetail = { plan ->
                                    selectedPlan = plan
                                }
                            )
                        }
                    }
                }
            }
        },
        containerColor = dialogContainerColor(),
        modifier = Modifier
            .fillMaxWidth(0.95f)
            .fillMaxHeight(0.8f)
    )

    // 任务详情弹窗
    selectedPlan?.let { plan ->
        MonitoringTaskDetailDialog(
            plan = plan,
            onDismiss = { selectedPlan = null },
            onEdit = { editPlan ->
                selectedPlan = null
                showEditDialog = true
                // TODO: 实现编辑功能
            },
            onDelete = { deletePlan ->
                scope.launch {
                    try {
                        monitoringPlanRepository.deleteMonitoringPlanById(deletePlan.id)
                        // 刷新列表
                        monitoringPlans = monitoringPlanRepository.getAllMonitoringPlans().first()
                    } catch (e: Exception) {
                        // 处理删除错误
                    }
                }
            }
        )
    }
}

/**
 * 监控任务项组件
 */
@Composable
fun MonitoringTaskItem(
    plan: MonitoringPlanEntity,
    executionResult: MonitoringTaskResult?,
    isExecuting: Boolean,
    onExecute: (MonitoringPlanEntity) -> Unit,
    onShowDetail: (MonitoringPlanEntity) -> Unit
) {
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }

    // 获取监控状态，每秒更新一次
    var monitoringStatus by remember { mutableStateOf(MonitoringStatusUtils.getMonitoringStatus(plan)) }

    // 定时更新状态（仅对等待中的任务）
    LaunchedEffect(plan.id, monitoringStatus.isWaiting) {
        if (monitoringStatus.isWaiting) {
            while (true) {
                delay(1000) // 每秒更新一次
                monitoringStatus = MonitoringStatusUtils.getMonitoringStatus(plan)
            }
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onShowDetail(plan) },
        colors = CardDefaults.cardColors(
            containerColor = if (plan.isEnabled) Color.White else Color.Gray.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 任务标题和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = plan.name,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    Row {
                        Text(
                            text = when (plan.type) {
                                MonitoringType.CART -> "购物车"
                                MonitoringType.PRODUCT_DETAIL -> "商品详情"
                            },
                            fontSize = 12.sp,
                            color = Color.Gray
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = when (plan.operationType) {
                                MonitoringOperationType.INTERVAL -> "间隔监控"
                                MonitoringOperationType.SCHEDULED -> "定时监控"
                                MonitoringOperationType.MANUAL -> "手动监控"
                            },
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }

                    // 显示等待状态
                    if (monitoringStatus.isWaiting || plan.operationType != MonitoringOperationType.MANUAL) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 状态指示器
                            Box(
                                modifier = Modifier
                                    .size(8.dp)
                                    .background(
                                        color = monitoringStatus.statusColor,
                                        shape = CircleShape
                                    )
                            )

                            Spacer(modifier = Modifier.width(6.dp))

                            Text(
                                text = monitoringStatus.statusText,
                                fontSize = 11.sp,
                                color = monitoringStatus.statusColor,
                                fontWeight = FontWeight.Medium
                            )

                            // 显示下次执行时间
                            if (monitoringStatus.nextExecutionTime != null) {
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "下次: ${MonitoringStatusUtils.formatNextExecutionTime(monitoringStatus.nextExecutionTime)}",
                                    fontSize = 10.sp,
                                    color = Color.Gray
                                )
                            }
                        }
                    }
                }

                // 执行按钮
                IconButton(
                    onClick = { onExecute(plan) },
                    enabled = plan.isEnabled && !isExecuting
                ) {
                    Icon(
                        Icons.Default.PlayArrow,
                        contentDescription = "执行",
                        tint = if (plan.isEnabled) MaterialTheme.colorScheme.primary else Color.Gray
                    )
                }
            }

            // 任务详情
            Spacer(modifier = Modifier.height(8.dp))

            Row {
                Text(
                    text = "商品数量: ${plan.productIds.size}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )

                Spacer(modifier = Modifier.width(16.dp))

                Text(
                    text = "优先级: ${plan.priority}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )

                Spacer(modifier = Modifier.width(16.dp))

                Text(
                    text = "执行次数: ${plan.executedCount}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 显示间隔信息（仅间隔监控）
            if (plan.operationType == MonitoringOperationType.INTERVAL) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "监控间隔: ${plan.intervalSeconds}秒",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 显示定时配置（仅定时监控）
            if (plan.operationType == MonitoringOperationType.SCHEDULED && plan.scheduledConfig.isNotEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "定时配置: ${plan.scheduledConfig}",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 最后执行时间
            plan.lastExecutedAt?.let { lastExecuted ->
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "最后执行: ${dateFormat.format(lastExecuted)}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 执行结果
            executionResult?.let { result ->
                Spacer(modifier = Modifier.height(8.dp))
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = if (result.success) Color.Green.copy(alpha = 0.1f) else Color.Red.copy(
                            alpha = 0.1f
                        )
                    )
                ) {
                    Column(modifier = Modifier.padding(8.dp)) {
                        Text(
                            text = if (result.success) "✓ 执行成功" else "✗ 执行失败",
                            fontSize = 12.sp,
                            color = if (result.success) Color.Green else Color.Red,
                            fontWeight = FontWeight.Bold
                        )

                        Text(
                            text = result.message,
                            fontSize = 11.sp,
                            color = Color.Gray,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )

                        if (result.success && (result.changesDetected > 0 || result.importantChanges > 0)) {
                            Text(
                                text = "检测到 ${result.changesDetected} 个变化 (${result.importantChanges} 个重要)",
                                fontSize = 11.sp,
                                color = Color.Blue
                            )
                        }
                    }
                }
            }
        }
    }
}
